{"name": "merchant-insights-mock-server", "version": "1.0.0", "description": "Mock server for Merchant Insights analytics API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-endpoints.js"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "react-redux": "^9.2.0"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mock", "server", "api", "analytics", "merchant-insights"], "author": "Merchant Insights Team", "license": "MIT"}