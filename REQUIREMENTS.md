Merchant Insights
Description
This is a description for the new UI for Merchant Insights. Merchant Insights is an application which allows a merchant to view insights for his/her transactions from NBG customers along with comparison with the competition.
There will be 4 tabs in the statistics page of the new Merchant Insights UI which are described below. In each tab, at the top left corner the logo for NBG will be shown, while in the top right corner we will have a button to change the language between English and Greek and also show the Merchant’s Name next to it.

Tab 1 - Dashboard
Metrics Dashboard
Overview of transaction volumes for the selected time period
Cards with metrics for merchant and competitors:
Total transactions
Total revenue
Average Amount per transaction
Six cards, first row with the three merchant metrics, second row with the three competitor metrics.
Show the absolute value and then show the percentage change (with red/green down/up arrow) from the same period of the previous year.
Graphs for transactions, revenue and customers over time comparing merchant and competitors.
Customization: default bars, else line or table
Timeline: default daily, else monthly, weekly, yearly
X-axis: if daily show the exact date, if weekly, the first day of that week, if monthly the month, if yearly the year, Y-axis - Value (transactions, revenue or customers), Legend: color for Merchant and Competition
Hover: 
if daily show the exact date, if weekly, the first day of that week, if monthly the month, if yearly the year
Merchant or competition
Value
Percentage change (with red/green down/up arrow) for same period of the previous year
**For customers: do not show customers for competition (compliance)
Filters - sidebar on the left side of the screen with the following sections of filters:
Date Range in calendar form: default latest month to today - starting from 1/1/23 and ending to today-1
Channel: e-commerce, physical
Demographics:
Gender
Age Group
Customer Location
Go For More Customers [only available if the merchant is Go For More]
Shopping Interests
Tab 2  - Revenue
Revenue Analytics
Deep dive in revenue analytics for the merchant and competition
Cards with the following metrics only for the merchant
Total revenue
Average daily revenue
Average amount per transaction
Also, if the merchant is Go For More show the following metrics:
Total revenue from Go For More transactions
Total rewarded amount from Go For More transactions
Total redeemed amount from Go For More transactions
Six cards, first row with the three revenue metrics, second row with the three Go For More metrics.
Revenue trend graph merchant vs competition
Customization: default bars, else line or table
Timeline: default weekly, else monthly, quarterly
X-axis: if weekly, show the first day of that week, if monthly the month, if quarterly the quarter, Y-axis - Value, Legend: color for Merchant and Competition
Hover: 
if weekly, the first day of that week, if monthly the month, if quarterly the quarter
Value
Percentage change (with red/green down/up arrow) for previous week (or month or quarter)
Revenue percentage change graph from last year for the merchant
Line chart which show the revenue percentage change from last year per week of the selected period
X-axis: week, Y-axis - Value (percentage change)
Revenue by shopping interests graph merchant vs competition
Customization: default bars, else line or table
Timeline: default weekly, else monthly, quarterly
X-axis: if weekly, show the first day of that week, if monthly the month, if yearly the year, Y-axis - Value, Legend: color for Merchant and Competition
Hover: 
if weekly, the first day of that week, if monthly the month, if quarterly the quarter
Shopping interest
Value
Revenue by channel merchant vs competition
Customization: default two bars one for merchant, one for competition like the following:

 else bar chart or table
Hover: absolute value, percentage over total
Filters - sidebar on the left side of the screen with the following sections of filters:
Date Range in calendar form: default latest month to today - starting from 1/1/23 and ending to today-1
Channel: e-commerce, physical
Demographics:
Gender
Age Group
Customer Location
Go For More Customers [only available if the merchant is Go For More]
Shopping Interests

Tab 3  - Customer Demographics
Customer Analytics
Customer demographics and behavioral patterns
Cards with the following metrics:
Total customers
New customers (customers who haven’t made a transaction in the merchant in the previous year and made in the selected period)
Returning customers (customers who have made a transaction in the merchant in the previous year and made in the selected period)
Top spenders customers (customers who belong in the top 20% of spending for the period? Or for YTD)
Loyal customers: defined as customers who make regular purchases (at least x per month or use RFM scoring and take only customers who make purchases in the merchant Merchant Promotions_Data-driven Filtering RFM)
At risk customers (customers who have made many transactions with high amount in the past year? But haven’t made a transaction recently
	Six cards: two rows and three columns.
Customers by gender graph
Two bars like the following image, one for the merchant and below it one for competition.
For merchant on hover also show the absolute values.

Customers by age_group graph
A graph like the following for the merchant, with a bold line in each row indicating the competition’s percentage (i.e. a horizontal bar chart with reference line). Also show the competition percentage in the end, beside the merchant’s percentage (in parentheses with different colors). On hover, show the absolute number of customers for the merchant along with the percentages for merchant and competition.

Customers by shopping frequency merchant vs competition graph
In a bar chart show with different colors for merchant and competition, the percentage of customers per transaction group (1 trn, 2 trns, 3 trns, 4-10 trns, 10+ trns).
X-axis: transaction group, Y-axis - Value (percentage of customers), Legend: color for Merchant and Competition
Hover: transaction group, Merchant or competition, Percentage and absolute value only for the Merchant
Customers by shopping interests merchant vs competition graph
Bar chart with percentage of customers by interest for merchant and competition. Only show values with a percentage above 10% for either merchant or competition, group all other values as other with their total percentage.
X-axis: shopping interest value, Y-axis - Value (percentage of customers), Legend: color for Merchant and Competition
Hover: shopping interests, Merchant or competition, Percentage and absolute value only for the Merchant
Filters - sidebar on the left side of the screen with the following sections of filters:
Date Range in calendar form: default latest month to today - starting from 1/1/23 and ending to today-1
Channel: e-commerce, physical
Demographics:
Gender
Age Group
Customer Location
Go For More Customers [only available if the merchant is Go For More]
Shopping Interests

Tab 4 - Current dashboard is remaed to competition

Appendix
Below is the available values for the filters:
Gender (dropdown - single selection)
All
Male
Female
Age Group (dropdown - multiple selection)
All
Generation Z (18-24)
Millennials (25-40)
Generation X (41-56)
Baby Βoomers (57-75)
Silent Generation (76-96)
Channel (dropdown - single selection)
All
Physical Stores
E-Commerce
Go For More Customers (dropdown - single selection)
Yes
No
Shopping Interests (dropdown - multiple selection)
Automotive & Fuel Products
Electronics & Household Appliances
Telecommunication
Health & Medical Care
Entertainment & Hobbies
Education
Toys
Travel & Transportation
Personal Care
Pets
Fashion, Cosmetics & Jewelry
Tourism
Home & Garden
Restaurants, Bars, Fast Food & Coffee
Food & Drinks
Customer Location  (dropdown - multiple selection of location trees)
All Greek Regions (i.e. ΑΤΤΙΚΗ, ΚΡΗΤΗ, ΚΕΝΤΡΙΚΗ ΜΑΚΕΔΟΝΙΑ etc.), Regional Units (i.e. ΒΟΡΕΙΟΣ ΤΟΜΕΑΣ ΑΘΗΝΩΝ, ΧΑΝΙΑ, ΘΕΣΣΑΛΟΝΙΚΗ etc.) and municipalities (i.e. ΧΑΛΑΝΔΡΙ, ΣΦΑΚΙΑ, ΠΥΛΑΙΑ, etc.) where under each Region we show the corresponding regional units and under each regional unit the corresponding municipalities like below:

Store (dropdown - multiple selection)
All
Store name 1
Store name 2
